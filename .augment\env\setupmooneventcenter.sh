#!/bin/bash

# Moon Event Center Development Environment Setup
# This script sets up a React + TypeScript + Vite project with comprehensive testing

set -e  # Exit on any error

echo "🌙 Setting up Moon Event Center development environment..."

# Update system packages
echo "📦 Updating system packages..."
sudo apt-get update -qq

# Install Node.js 20 (LTS) and npm
echo "🟢 Installing Node.js 20 LTS..."
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify Node.js and npm installation
echo "✅ Verifying Node.js installation..."
node --version
npm --version

# Navigate to project directory
cd /mnt/persist/workspace/moon-event-center

# Install project dependencies
echo "📚 Installing project dependencies..."
npm ci --silent

# Install Playwright browsers for E2E testing
echo "🎭 Installing Playwright browsers..."
npx playwright install --with-deps

# Verify TypeScript compilation
echo "🔧 Verifying TypeScript compilation..."
npx tsc --noEmit

# Run ESLint to check code quality
echo "🔍 Running ESLint checks..."
npm run lint

# Add Node.js and npm to PATH in user profile
echo "🔧 Adding Node.js to PATH..."
echo 'export PATH="/usr/bin:$PATH"' >> $HOME/.profile

echo "✅ Development environment setup complete!"
echo "🧪 Ready to run tests..."