import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEOHead from '../components/seo/SEOHead';

interface Package {
  id: string;
  title: string;
  subtitle: string;
  price: string;
  duration: string;
  capacity: string;
  features: string[];
  highlights: string[];
  popular?: boolean;
}

const ServicesPage: React.FC = () => {
  const packages: Package[] = [
    {
      id: 'wedding-classic',
      title: 'Classic Wedding',
      subtitle: 'Perfect for intimate celebrations',
      price: 'Starting at $2,500',
      duration: '6 hours',
      capacity: 'Up to 150 guests',
      features: [
        'Bridal suite access',
        'Ceremony & reception space',
        'Tables, chairs & linens',
        'Basic lighting package',
        'Sound system',
        'Dance floor',
        'Event coordinator',
        'Setup & cleanup'
      ],
      highlights: [
        'Complimentary consultation',
        'Flexible decoration policy',
        'Preferred vendor list'
      ]
    },
    {
      id: 'wedding-premium',
      title: 'Premium Wedding',
      subtitle: 'Luxury experience for your special day',
      price: 'Starting at $4,200',
      duration: '8 hours',
      capacity: 'Up to 250 guests',
      popular: true,
      features: [
        'Everything in Classic package',
        'Premium lighting design',
        'Upgraded sound system',
        'Outdoor ceremony option',
        'Cocktail hour space',
        'Enhanced bridal suite',
        'Dedicated day-of coordinator',
        'Valet parking service'
      ],
      highlights: [
        'Complimentary tasting for 4',
        'Wedding planning timeline',
        'Vendor coordination'
      ]
    },
    {
      id: 'wedding-luxury',
      title: 'Luxury Wedding',
      subtitle: 'The ultimate wedding experience',
      price: 'Starting at $6,800',
      duration: '10 hours',
      capacity: 'Up to 300 guests',
      features: [
        'Everything in Premium package',
        'Custom lighting design',
        'Professional AV system',
        'Multiple ceremony options',
        'Private cocktail lounge',
        'Luxury bridal & groom suites',
        'Full-service coordination team',
        'Complimentary champagne service'
      ],
      highlights: [
        'Personal wedding planner',
        'Rehearsal dinner discount',
        'Anniversary celebration credit'
      ]
    },
    {
      id: 'corporate-standard',
      title: 'Corporate Standard',
      subtitle: 'Professional meetings & events',
      price: 'Starting at $1,800',
      duration: '4 hours',
      capacity: 'Up to 100 guests',
      features: [
        'Meeting room setup',
        'AV equipment package',
        'Presentation screen',
        'Wireless microphones',
        'Coffee & refreshment station',
        'Professional lighting',
        'Event support staff',
        'Parking included'
      ],
      highlights: [
        'Flexible room configurations',
        'Catering partnerships',
        'Technology support'
      ]
    },
    {
      id: 'corporate-executive',
      title: 'Corporate Executive',
      subtitle: 'Premium corporate experiences',
      price: 'Starting at $3,200',
      duration: '6 hours',
      capacity: 'Up to 200 guests',
      features: [
        'Everything in Standard package',
        'Executive boardroom access',
        'Premium AV setup',
        'Live streaming capabilities',
        'Gourmet catering options',
        'Dedicated event manager',
        'VIP reception area',
        'Valet parking service'
      ],
      highlights: [
        'Custom branding options',
        'Photography services',
        'Post-event analytics'
      ]
    },
    {
      id: 'social-celebration',
      title: 'Social Celebration',
      subtitle: 'Perfect for birthdays, anniversaries & more',
      price: 'Starting at $1,200',
      duration: '4 hours',
      capacity: 'Up to 80 guests',
      features: [
        'Flexible room setup',
        'Basic sound system',
        'Decorating freedom',
        'Tables & chairs',
        'Kitchen access',
        'Event support',
        'Cleanup service',
        'Parking included'
      ],
      highlights: [
        'BYOB policy',
        'Custom decoration options',
        'Extended hours available'
      ]
    }
  ];

  const fadeInUp = {
    initial: { opacity: 0, y: 30 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  return (
    <div className="min-h-screen">
      <SEOHead
        title="Wedding & Event Services - Moon Event Center Packages"
        description="Explore our comprehensive wedding and event packages at Moon Event Center in Richardson, Texas. From intimate ceremonies to grand celebrations, we have the perfect package for your special day."
        keywords={[
          'wedding packages Richardson TX',
          'event services Richardson',
          'wedding venue packages',
          'corporate event packages',
          'quinceañera packages',
          'anniversary celebration packages',
          'event planning services',
          'wedding reception packages'
        ]}
        url="https://mooneventcenter.com/services"
        type="website"
      />

      {/* Hero Section */}
      <section className="section-padding bg-moon-white">
        <div className="container-max">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-serif font-bold text-moon-navy mb-6">
              Services & Packages
            </h1>
            <p className="text-xl md:text-2xl text-moon-navy max-w-3xl mx-auto leading-relaxed">
              Comprehensive event packages designed to make your celebration extraordinary
            </p>
          </motion.div>
        </div>
      </section>

      {/* Packages Grid */}
      <section className="section-padding">
        <div className="container-max">
          <motion.div
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8"
          >
            {packages.map((pkg, index) => (
              <motion.div
                key={pkg.id}
                variants={fadeInUp}
                className={`relative bg-moon-white rounded-lg shadow-lg overflow-hidden ${
                  pkg.popular ? 'ring-2 ring-moon-gold' : ''
                }`}
              >
                {pkg.popular && (
                  <div className="absolute top-0 right-0 bg-moon-gold text-moon-white px-4 py-2 text-sm font-semibold">
                    Most Popular
                  </div>
                )}

                <div className="p-8">
                  <div className="mb-6">
                    <h3 className="text-2xl font-serif font-bold text-moon-navy mb-2">
                      {pkg.title}
                    </h3>
                    <p className="text-moon-navy mb-4">{pkg.subtitle}</p>
                    <div className="flex items-baseline gap-4 mb-4">
                      <span className="text-3xl font-bold text-moon-gold">{pkg.price}</span>
                    </div>
                    <div className="flex gap-6 text-sm text-moon-navy">
                      <span>⏱️ {pkg.duration}</span>
                      <span>👥 {pkg.capacity}</span>
                    </div>
                  </div>

                  <div className="mb-6">
                    <h4 className="font-semibold text-moon-navy mb-3">Package Includes:</h4>
                    <ul className="space-y-2">
                      {pkg.features.map((feature, idx) => (
                        <li key={idx} className="flex items-start text-sm text-moon-navy">
                          <span className="w-2 h-2 bg-moon-gold rounded-full mr-3 mt-2 flex-shrink-0"></span>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="mb-6">
                    <h4 className="font-semibold text-moon-navy mb-3">Special Highlights:</h4>
                    <ul className="space-y-2">
                      {pkg.highlights.map((highlight, idx) => (
                        <li key={idx} className="flex items-start text-sm text-moon-gold">
                          <span className="mr-2">✨</span>
                          {highlight}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="flex gap-3">
                    <Link
                      to="/contact"
                      className="btn-primary flex-1 text-center"
                    >
                      Book Now
                    </Link>
                    <button className="btn-outline flex-1">
                      Learn More
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Additional Services */}
      <section className="section-padding bg-moon-white">
        <div className="container-max">
          <motion.div
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <motion.h2
              variants={fadeInUp}
              className="text-3xl md:text-4xl font-serif font-bold text-moon-navy mb-6"
            >
              Additional Services
            </motion.h2>
            <motion.p
              variants={fadeInUp}
              className="text-xl text-moon-navy max-w-3xl mx-auto leading-relaxed"
            >
              Enhance your event with our premium add-on services
            </motion.p>
          </motion.div>

          <motion.div
            variants={staggerContainer}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-6"
          >
            {[
              {
                icon: '🎵',
                title: 'DJ Services',
                description: 'Professional DJ with premium sound system and lighting',
                price: 'From $800'
              },
              {
                icon: '📸',
                title: 'Photography',
                description: 'Professional event photography and videography services',
                price: 'From $1,200'
              },
              {
                icon: '🍽️',
                title: 'Catering',
                description: 'Gourmet catering from our preferred partner restaurants',
                price: 'From $35/person'
              },
              {
                icon: '💐',
                title: 'Floral Design',
                description: 'Custom floral arrangements and venue decoration',
                price: 'From $500'
              }
            ].map((service, index) => (
              <motion.div
                key={service.title}
                variants={fadeInUp}
                className="bg-moon-white p-6 rounded-lg shadow-lg text-center"
              >
                <div className="text-4xl mb-4">{service.icon}</div>
                <h3 className="text-lg font-semibold text-moon-navy mb-2">{service.title}</h3>
                <p className="text-moon-navy text-sm mb-4">{service.description}</p>
                <p className="text-moon-gold font-semibold">{service.price}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section-padding bg-moon-white">
        <div className="container-max">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-moon-navy mb-6">
              Ready to Plan Your Event?
            </h2>
            <p className="text-xl text-moon-navy mb-8 max-w-2xl mx-auto">
              Contact us today for a personalized consultation and let us help you create an unforgettable experience.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/contact" className="btn-secondary">
                Schedule Consultation
              </Link>
              <a href="tel:+19725058888" className="btn-outline border-moon-white text-moon-white hover:bg-moon-white hover:text-moon-navy">
                Call (*************
              </a>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default ServicesPage;
