import React, { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';
import type { Booking, BookingFormData, BookingFilter, BookingStats, PaymentRecord, Message, Favorite, UserPreferences } from '../types/booking';
import { useAuth } from './AuthContext';

export interface BookingContextType {
  // Bookings
  bookings: Booking[];
  isLoading: boolean;
  createBooking: (bookingData: BookingFormData) => Promise<{ success: boolean; error?: string; bookingId?: string }>;
  updateBooking: (bookingId: string, updates: Partial<Booking>) => Promise<{ success: boolean; error?: string }>;
  cancelBooking: (bookingId: string, reason?: string) => Promise<{ success: boolean; error?: string }>;
  getBookingById: (bookingId: string) => Booking | undefined;
  getUserBookings: (userId: string, filter?: BookingFilter) => Booking[];
  getBookingStats: (userId?: string) => BookingStats;
  
  // Payments
  payments: PaymentRecord[];
  addPayment: (bookingId: string, amount: number, method: PaymentRecord['paymentMethod']) => Promise<{ success: boolean; error?: string }>;
  getBookingPayments: (bookingId: string) => PaymentRecord[];
  
  // Messages
  messages: Message[];
  sendMessage: (toUserId: string, subject: string, content: string, bookingId?: string) => Promise<{ success: boolean; error?: string }>;
  markMessageAsRead: (messageId: string) => Promise<{ success: boolean; error?: string }>;
  getUserMessages: (userId: string) => Message[];
  getUnreadCount: (userId: string) => number;
  
  // Favorites
  favorites: Favorite[];
  addToFavorites: (type: Favorite['type'], itemId: string, itemData: Favorite['itemData']) => Promise<{ success: boolean; error?: string }>;
  removeFromFavorites: (favoriteId: string) => Promise<{ success: boolean; error?: string }>;
  getUserFavorites: (userId: string) => Favorite[];
  isFavorite: (itemId: string, type: Favorite['type']) => boolean;
  
  // User Preferences
  userPreferences: UserPreferences | null;
  updateUserPreferences: (preferences: Partial<UserPreferences>) => Promise<{ success: boolean; error?: string }>;
}

const BookingContext = createContext<BookingContextType | undefined>(undefined);

export const useBooking = () => {
  const context = useContext(BookingContext);
  if (context === undefined) {
    throw new Error('useBooking must be used within a BookingProvider');
  }
  return context;
};

interface BookingProviderProps {
  children: ReactNode;
}

export const BookingProvider: React.FC<BookingProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [payments, setPayments] = useState<PaymentRecord[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [favorites, setFavorites] = useState<Favorite[]>([]);
  const [userPreferences, setUserPreferences] = useState<UserPreferences | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Simulate API calls
  const simulateApiCall = <T,>(data: T, delay: number = 1000): Promise<T> => {
    return new Promise((resolve) => {
      setTimeout(() => resolve(data), delay);
    });
  };

  // Load initial data when user changes
  useEffect(() => {
    if (user) {
      loadUserData();
    } else {
      // Clear data when user logs out
      setBookings([]);
      setPayments([]);
      setMessages([]);
      setFavorites([]);
      setUserPreferences(null);
    }
  }, [user]);

  const loadUserData = async () => {
    if (!user) return;
    
    setIsLoading(true);
    try {
      // Load mock data from localStorage or create initial data
      const savedBookings = localStorage.getItem(`moon_bookings_${user.id}`);
      const savedPayments = localStorage.getItem(`moon_payments_${user.id}`);
      const savedMessages = localStorage.getItem(`moon_messages_${user.id}`);
      const savedFavorites = localStorage.getItem(`moon_favorites_${user.id}`);
      const savedPreferences = localStorage.getItem(`moon_preferences_${user.id}`);

      if (savedBookings) {
        setBookings(JSON.parse(savedBookings));
      } else {
        // Create sample booking for demo
        const sampleBooking = createSampleBooking(user.id);
        setBookings([sampleBooking]);
        localStorage.setItem(`moon_bookings_${user.id}`, JSON.stringify([sampleBooking]));
      }

      if (savedPayments) {
        setPayments(JSON.parse(savedPayments));
      }

      if (savedMessages) {
        setMessages(JSON.parse(savedMessages));
      }

      if (savedFavorites) {
        setFavorites(JSON.parse(savedFavorites));
      }

      if (savedPreferences) {
        setUserPreferences(JSON.parse(savedPreferences));
      } else {
        // Create default preferences
        const defaultPreferences = createDefaultPreferences();
        setUserPreferences(defaultPreferences);
        localStorage.setItem(`moon_preferences_${user.id}`, JSON.stringify(defaultPreferences));
      }
    } finally {
      setIsLoading(false);
    }
  };

  const createSampleBooking = (userId: string): Booking => {
    const eventDate = new Date();
    eventDate.setMonth(eventDate.getMonth() + 2); // 2 months from now
    
    return {
      id: `booking_${Date.now()}`,
      userId,
      eventType: 'wedding',
      eventDate: eventDate.toISOString().split('T')[0],
      eventTime: '18:00',
      guestCount: 150,
      packageType: 'gold',
      status: 'confirmed',
      totalAmount: 8500,
      paidAmount: 2500,
      paymentStatus: 'partial',
      contactPerson: {
        firstName: user?.firstName || 'Guest',
        lastName: user?.lastName || 'User',
        email: user?.email || '<EMAIL>',
        phone: user?.phone || '+****************'
      },
      eventDetails: {
        title: 'Sarah & Michael\'s Wedding',
        description: 'Elegant evening wedding celebration',
        specialRequests: 'Vegetarian menu options, live band setup',
        decorationTheme: 'Celestial/Moon theme with navy and silver',
        musicRequirements: 'Live band for ceremony, DJ for reception'
      },
      venue: {
        mainHall: true,
        bridalRoom: true,
        additionalRooms: ['Cocktail area']
      },
      services: {
        catering: true,
        photography: true,
        decoration: true,
        music: true,
        lighting: true,
        security: false
      },
      timeline: {
        setupTime: '14:00',
        eventStart: '18:00',
        eventEnd: '23:00',
        cleanupTime: '01:00'
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      notes: 'Looking forward to a magical evening!'
    };
  };

  const createDefaultPreferences = (): UserPreferences => ({
    notifications: {
      email: true,
      sms: true,
      bookingReminders: true,
      promotionalEmails: false,
      eventUpdates: true
    },
    privacy: {
      profileVisibility: 'private',
      shareContactInfo: false,
      allowMarketing: false
    },
    communication: {
      preferredContactMethod: 'email',
      timezone: 'America/Chicago',
      language: 'en'
    }
  });

  // Booking methods
  const createBooking = async (bookingData: BookingFormData) => {
    if (!user) return { success: false, error: 'Not authenticated' };

    setIsLoading(true);
    try {
      await simulateApiCall(null, 1500);

      const newBooking: Booking = {
        id: `booking_${Date.now()}`,
        userId: user.id,
        ...bookingData,
        status: 'pending',
        totalAmount: calculateBookingTotal(bookingData),
        paidAmount: 0,
        paymentStatus: 'pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const updatedBookings = [...bookings, newBooking];
      setBookings(updatedBookings);
      localStorage.setItem(`moon_bookings_${user.id}`, JSON.stringify(updatedBookings));

      return { success: true, bookingId: newBooking.id };
    } catch (error) {
      return { success: false, error: 'Failed to create booking' };
    } finally {
      setIsLoading(false);
    }
  };

  const calculateBookingTotal = (bookingData: BookingFormData): number => {
    // Base package prices
    const packagePrices = {
      platinum: 12000,
      gold: 8500,
      silver: 5500,
      custom: 7000
    };

    let total = packagePrices[bookingData.packageType];

    // Add service costs
    if (bookingData.services.catering) total += 2000;
    if (bookingData.services.photography) total += 1500;
    if (bookingData.services.decoration) total += 1000;
    if (bookingData.services.music) total += 800;
    if (bookingData.services.lighting) total += 600;
    if (bookingData.services.security) total += 400;

    // Guest count multiplier for larger events
    if (bookingData.guestCount > 200) {
      total += (bookingData.guestCount - 200) * 25;
    }

    return total;
  };

  const updateBooking = async (bookingId: string, updates: Partial<Booking>) => {
    setIsLoading(true);
    try {
      await simulateApiCall(null, 1000);

      const updatedBookings = bookings.map(booking =>
        booking.id === bookingId
          ? { ...booking, ...updates, updatedAt: new Date().toISOString() }
          : booking
      );

      setBookings(updatedBookings);
      if (user) {
        localStorage.setItem(`moon_bookings_${user.id}`, JSON.stringify(updatedBookings));
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to update booking' };
    } finally {
      setIsLoading(false);
    }
  };

  const cancelBooking = async (bookingId: string, reason?: string) => {
    return updateBooking(bookingId, { 
      status: 'cancelled', 
      notes: reason ? `Cancelled: ${reason}` : 'Cancelled by user' 
    });
  };

  const getBookingById = (bookingId: string) => {
    return bookings.find(booking => booking.id === bookingId);
  };

  const getUserBookings = (userId: string, filter?: BookingFilter) => {
    let userBookings = bookings.filter(booking => booking.userId === userId);

    if (filter) {
      if (filter.status?.length) {
        userBookings = userBookings.filter(booking => filter.status!.includes(booking.status));
      }
      if (filter.eventType?.length) {
        userBookings = userBookings.filter(booking => filter.eventType!.includes(booking.eventType));
      }
      if (filter.dateRange) {
        userBookings = userBookings.filter(booking => 
          booking.eventDate >= filter.dateRange!.start && 
          booking.eventDate <= filter.dateRange!.end
        );
      }
      if (filter.paymentStatus?.length) {
        userBookings = userBookings.filter(booking => filter.paymentStatus!.includes(booking.paymentStatus));
      }
    }

    return userBookings.sort((a, b) => new Date(b.eventDate).getTime() - new Date(a.eventDate).getTime());
  };

  const getBookingStats = (userId?: string): BookingStats => {
    const relevantBookings = userId ? getUserBookings(userId) : bookings;
    
    return {
      total: relevantBookings.length,
      pending: relevantBookings.filter(b => b.status === 'pending').length,
      confirmed: relevantBookings.filter(b => b.status === 'confirmed').length,
      completed: relevantBookings.filter(b => b.status === 'completed').length,
      cancelled: relevantBookings.filter(b => b.status === 'cancelled').length,
      totalRevenue: relevantBookings.reduce((sum, b) => sum + b.paidAmount, 0),
      pendingPayments: relevantBookings.reduce((sum, b) => sum + (b.totalAmount - b.paidAmount), 0)
    };
  };

  // Payment methods
  const addPayment = async (bookingId: string, amount: number, method: PaymentRecord['paymentMethod']) => {
    if (!user) return { success: false, error: 'Not authenticated' };

    setIsLoading(true);
    try {
      await simulateApiCall(null, 1000);

      const newPayment: PaymentRecord = {
        id: `payment_${Date.now()}`,
        bookingId,
        amount,
        paymentMethod: method,
        status: 'completed',
        processedAt: new Date().toISOString()
      };

      const updatedPayments = [...payments, newPayment];
      setPayments(updatedPayments);
      localStorage.setItem(`moon_payments_${user.id}`, JSON.stringify(updatedPayments));

      // Update booking payment status
      const booking = getBookingById(bookingId);
      if (booking) {
        const newPaidAmount = booking.paidAmount + amount;
        const paymentStatus = newPaidAmount >= booking.totalAmount ? 'paid' : 'partial';
        await updateBooking(bookingId, { paidAmount: newPaidAmount, paymentStatus });
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Payment processing failed' };
    } finally {
      setIsLoading(false);
    }
  };

  const getBookingPayments = (bookingId: string) => {
    return payments.filter(payment => payment.bookingId === bookingId);
  };

  // Message methods
  const sendMessage = async (toUserId: string, subject: string, content: string, bookingId?: string) => {
    if (!user) return { success: false, error: 'Not authenticated' };

    setIsLoading(true);
    try {
      await simulateApiCall(null, 800);

      const newMessage: Message = {
        id: `message_${Date.now()}`,
        bookingId,
        fromUserId: user.id,
        toUserId,
        subject,
        content,
        isRead: false,
        sentAt: new Date().toISOString()
      };

      const updatedMessages = [...messages, newMessage];
      setMessages(updatedMessages);
      localStorage.setItem(`moon_messages_${user.id}`, JSON.stringify(updatedMessages));

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to send message' };
    } finally {
      setIsLoading(false);
    }
  };

  const markMessageAsRead = async (messageId: string) => {
    setIsLoading(true);
    try {
      await simulateApiCall(null, 500);

      const updatedMessages = messages.map(message =>
        message.id === messageId ? { ...message, isRead: true } : message
      );

      setMessages(updatedMessages);
      if (user) {
        localStorage.setItem(`moon_messages_${user.id}`, JSON.stringify(updatedMessages));
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to mark message as read' };
    } finally {
      setIsLoading(false);
    }
  };

  const getUserMessages = (userId: string) => {
    return messages
      .filter(message => message.toUserId === userId || message.fromUserId === userId)
      .sort((a, b) => new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime());
  };

  const getUnreadCount = (userId: string) => {
    return messages.filter(message => message.toUserId === userId && !message.isRead).length;
  };

  // Favorites methods
  const addToFavorites = async (type: Favorite['type'], itemId: string, itemData: Favorite['itemData']) => {
    if (!user) return { success: false, error: 'Not authenticated' };

    setIsLoading(true);
    try {
      await simulateApiCall(null, 500);

      const newFavorite: Favorite = {
        id: `favorite_${Date.now()}`,
        userId: user.id,
        type,
        itemId,
        itemData,
        createdAt: new Date().toISOString()
      };

      const updatedFavorites = [...favorites, newFavorite];
      setFavorites(updatedFavorites);
      localStorage.setItem(`moon_favorites_${user.id}`, JSON.stringify(updatedFavorites));

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to add to favorites' };
    } finally {
      setIsLoading(false);
    }
  };

  const removeFromFavorites = async (favoriteId: string) => {
    if (!user) return { success: false, error: 'Not authenticated' };

    setIsLoading(true);
    try {
      await simulateApiCall(null, 500);

      const updatedFavorites = favorites.filter(favorite => favorite.id !== favoriteId);
      setFavorites(updatedFavorites);
      localStorage.setItem(`moon_favorites_${user.id}`, JSON.stringify(updatedFavorites));

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to remove from favorites' };
    } finally {
      setIsLoading(false);
    }
  };

  const getUserFavorites = (userId: string) => {
    return favorites
      .filter(favorite => favorite.userId === userId)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  };

  const isFavorite = (itemId: string, type: Favorite['type']) => {
    if (!user) return false;
    return favorites.some(favorite => 
      favorite.userId === user.id && 
      favorite.itemId === itemId && 
      favorite.type === type
    );
  };

  // User preferences methods
  const updateUserPreferences = async (preferences: Partial<UserPreferences>) => {
    if (!user) return { success: false, error: 'Not authenticated' };

    setIsLoading(true);
    try {
      await simulateApiCall(null, 800);

      const updatedPreferences = { ...userPreferences, ...preferences } as UserPreferences;
      setUserPreferences(updatedPreferences);
      localStorage.setItem(`moon_preferences_${user.id}`, JSON.stringify(updatedPreferences));

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to update preferences' };
    } finally {
      setIsLoading(false);
    }
  };

  const value: BookingContextType = {
    // Bookings
    bookings,
    isLoading,
    createBooking,
    updateBooking,
    cancelBooking,
    getBookingById,
    getUserBookings,
    getBookingStats,
    
    // Payments
    payments,
    addPayment,
    getBookingPayments,
    
    // Messages
    messages,
    sendMessage,
    markMessageAsRead,
    getUserMessages,
    getUnreadCount,
    
    // Favorites
    favorites,
    addToFavorites,
    removeFromFavorites,
    getUserFavorites,
    isFavorite,
    
    // User Preferences
    userPreferences,
    updateUserPreferences
  };

  return (
    <BookingContext.Provider value={value}>
      {children}
    </BookingContext.Provider>
  );
};
