import React from 'react';
import { motion } from 'framer-motion';

interface StatsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ComponentType<{ className?: string }>;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple';
  loading?: boolean;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  subtitle,
  icon: Icon,
  trend,
  color = 'blue',
  loading = false
}) => {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-600 border-blue-200',
    green: 'bg-green-50 text-green-600 border-green-200',
    yellow: 'bg-yellow-50 text-yellow-600 border-yellow-200',
    red: 'bg-red-50 text-red-600 border-red-200',
    purple: 'bg-purple-50 text-purple-600 border-purple-200'
  };

  const iconColorClasses = {
    blue: 'text-blue-500',
    green: 'text-green-500',
    yellow: 'text-yellow-500',
    red: 'text-red-500',
    purple: 'text-purple-500'
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      className={`bg-white rounded-lg shadow-md border border-moon-silver/20 p-6 ${colorClasses[color]}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-moon-gray mb-1">{title}</p>
          
          {loading ? (
            <div className="animate-pulse">
              <div className="h-8 bg-moon-silver/30 rounded w-20 mb-2"></div>
              {subtitle && <div className="h-4 bg-moon-silver/20 rounded w-16"></div>}
            </div>
          ) : (
            <>
              <p className="text-2xl font-bold text-moon-navy mb-1">
                {typeof value === 'number' ? value.toLocaleString() : value}
              </p>
              
              {subtitle && (
                <p className="text-sm text-moon-gray">{subtitle}</p>
              )}
              
              {trend && (
                <div className="flex items-center mt-2">
                  <span
                    className={`text-xs font-medium ${
                      trend.isPositive ? 'text-green-600' : 'text-red-600'
                    }`}
                  >
                    {trend.isPositive ? '+' : ''}{trend.value}%
                  </span>
                  <span className="text-xs text-moon-gray ml-1">vs last month</span>
                </div>
              )}
            </>
          )}
        </div>
        
        {Icon && (
          <div className={`p-3 rounded-lg ${iconColorClasses[color]} bg-opacity-10`}>
            <Icon className={`w-6 h-6 ${iconColorClasses[color]}`} />
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default StatsCard;
