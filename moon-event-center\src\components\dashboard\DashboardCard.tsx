import React from 'react';
import { motion } from 'framer-motion';

interface DashboardCardProps {
  title: string;
  children: React.ReactNode;
  className?: string;
  headerAction?: React.ReactNode;
  loading?: boolean;
}

const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  children,
  className = '',
  headerAction,
  loading = false
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`bg-white rounded-lg shadow-md border border-moon-silver/20 overflow-hidden ${className}`}
    >
      <div className="px-6 py-4 border-b border-moon-silver/20 bg-moon-silver/5">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-moon-navy">{title}</h3>
          {headerAction && (
            <div className="flex items-center space-x-2">
              {headerAction}
            </div>
          )}
        </div>
      </div>
      
      <div className="p-6">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-moon-gold"></div>
          </div>
        ) : (
          children
        )}
      </div>
    </motion.div>
  );
};

export default DashboardCard;
