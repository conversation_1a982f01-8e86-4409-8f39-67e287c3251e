{"hash": "ebabfafa", "configHash": "041b023a", "lockfileHash": "4e1281fd", "browserHash": "8eff581e", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "dc848f51", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "1a9e4734", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "09eb00c3", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "95c3e6a2", "needsInterop": true}, "@heroicons/react/24/outline": {"src": "../../@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "f87566ce", "needsInterop": false}, "@heroicons/react/24/solid": {"src": "../../@heroicons/react/24/solid/esm/index.js", "file": "@heroicons_react_24_solid.js", "fileHash": "2868e73e", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "e11c529c", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "67ce1cd6", "needsInterop": true}, "react-helmet-async": {"src": "../../react-helmet-async/lib/index.esm.js", "file": "react-helmet-async.js", "fileHash": "8b60be95", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "b7fc9864", "needsInterop": false}}, "chunks": {"chunk-2ZET3HRN": {"file": "chunk-2ZET3HRN.js"}, "chunk-S3Z6QACX": {"file": "chunk-S3Z6QACX.js"}, "chunk-IYDKXRZQ": {"file": "chunk-IYDKXRZQ.js"}}}