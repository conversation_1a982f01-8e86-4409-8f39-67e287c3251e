import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { motion } from 'framer-motion';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requiredRole?: 'customer' | 'staff' | 'admin';
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = true,
  requiredRole,
  redirectTo = '/auth'
}) => {
  const { isAuthenticated, user, isLoading } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-moon-navy/5 to-moon-silver/10">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center"
        >
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-moon-gold mx-auto mb-4"></div>
          <p className="text-moon-gray">Loading...</p>
        </motion.div>
      </div>
    );
  }

  // Check if authentication is required
  if (requireAuth && !isAuthenticated) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // Check if specific role is required
  if (requiredRole && user && user.role !== requiredRole) {
    // If user is staff/admin but trying to access customer-only content
    if (requiredRole === 'customer' && (user.role === 'staff' || user.role === 'admin')) {
      return <Navigate to="/staff" replace />;
    }
    
    // If user is customer but trying to access staff/admin content
    if ((requiredRole === 'staff' || requiredRole === 'admin') && user.role === 'customer') {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-moon-navy/5 to-moon-silver/10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-md mx-auto p-8"
          >
            <div className="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-moon-navy mb-2">Access Denied</h2>
            <p className="text-moon-gray mb-6">
              You don't have permission to access this page. This area is restricted to staff members only.
            </p>
            <button
              onClick={() => window.history.back()}
              className="bg-moon-gold text-white px-6 py-2 rounded-lg hover:bg-moon-gold/90 transition-colors"
            >
              Go Back
            </button>
          </motion.div>
        </div>
      );
    }

    // If user is staff but trying to access admin-only content
    if (requiredRole === 'admin' && user.role === 'staff') {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-moon-navy/5 to-moon-silver/10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-md mx-auto p-8"
          >
            <div className="bg-yellow-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-moon-navy mb-2">Admin Access Required</h2>
            <p className="text-moon-gray mb-6">
              This page requires administrator privileges. Please contact your system administrator if you need access.
            </p>
            <button
              onClick={() => window.history.back()}
              className="bg-moon-gold text-white px-6 py-2 rounded-lg hover:bg-moon-gold/90 transition-colors"
            >
              Go Back
            </button>
          </motion.div>
        </div>
      );
    }
  }

  // If all checks pass, render the protected content
  return <>{children}</>;
};

export default ProtectedRoute;
