import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import SEOHead from '../components/seo/SEOHead';

interface GalleryImage {
  id: number;
  src: string;
  alt: string;
  category: string;
  title: string;
  description: string;
}

const GalleryPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);

  const categories = [
    { id: 'all', name: 'All Events' },
    { id: 'weddings', name: 'Weddings' },
    { id: 'corporate', name: 'Corporate' },
    { id: 'social', name: 'Social Events' },
    { id: 'venue', name: 'Venue Features' }
  ];

  const galleryImages: GalleryImage[] = [
    {
      id: 1,
      src: '/assets/Images/Wedding Celebration Dancefloor.png',
      alt: 'Wedding Celebration Dancefloor',
      category: 'weddings',
      title: 'Elegant Wedding Reception',
      description: 'A beautiful wedding celebration with our signature lighting and elegant dancefloor setup.'
    },
    {
      id: 2,
      src: '/assets/Images/game day.png',
      alt: 'Corporate Game Day Event',
      category: 'corporate',
      title: 'Corporate Team Building',
      description: 'Professional corporate event featuring team building activities and networking opportunities.'
    },
    // Placeholder images for demonstration
    {
      id: 3,
      src: '/assets/Images/Wedding Celebration Dancefloor.png',
      alt: 'Ceremony Setup',
      category: 'weddings',
      title: 'Ceremony Space',
      description: 'Our beautiful ceremony space with elegant seating and floral arrangements.'
    },
    {
      id: 4,
      src: '/assets/Images/Corporate Event Celebration.png',
      alt: 'Corporate Event Celebration',
      category: 'corporate',
      title: 'Annual Corporate Gala',
      description: 'Sophisticated corporate gala with professional lighting and premium table settings.'
    },
    {
      id: 5,
      src: '/assets/Images/D&D Convention Celebration1.png',
      alt: 'D&D Convention Celebration',
      category: 'social',
      title: 'D&D Convention Celebration',
      description: 'Epic tabletop gaming convention with themed decorations and immersive atmosphere for fantasy enthusiasts.'
    },
    {
      id: 6,
      src: '/assets/Images/game day.png',
      alt: 'Venue Exterior',
      category: 'venue',
      title: 'Venue Exterior',
      description: 'The stunning exterior of Moon Event Center showcasing our architectural beauty.'
    }
  ];

  const filteredImages = selectedCategory === 'all'
    ? galleryImages
    : galleryImages.filter(img => img.category === selectedCategory);

  const fadeInUp = {
    initial: { opacity: 0, y: 30 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  return (
    <div className="min-h-screen">
      <SEOHead
        title="Wedding & Event Gallery - Moon Event Center Photos"
        description="Browse our stunning gallery of weddings and events at Moon Event Center in Richardson, Texas. See real celebrations and get inspired for your special day."
        keywords={[
          'wedding gallery Richardson TX',
          'event photos Richardson',
          'wedding venue photos',
          'Moon Event Center gallery',
          'wedding inspiration',
          'event venue photos',
          'Richardson wedding photos'
        ]}
        url="https://mooneventcenter.com/gallery"
        type="website"
      />

      {/* Hero Section */}
      <section className="section-padding bg-moon-white">
        <div className="container-max">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-serif font-bold text-moon-navy mb-6">
              Event Gallery
            </h1>
            <p className="text-xl md:text-2xl text-moon-navy max-w-3xl mx-auto leading-relaxed">
              Explore the magic of Moon Event Center through these captured moments
            </p>
          </motion.div>
        </div>
      </section>

      {/* Category Filter */}
      <section className="py-8 bg-moon-white">
        <div className="container-max">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex flex-wrap justify-center gap-4"
          >
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                  selectedCategory === category.id
                    ? 'bg-moon-navy text-moon-white shadow-lg'
                    : 'bg-moon-silver/20 text-moon-navy hover:bg-moon-silver/30'
                }`}
              >
                {category.name}
              </button>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Gallery Grid */}
      <section className="section-padding">
        <div className="container-max">
          <motion.div
            variants={staggerContainer}
            initial="initial"
            animate="animate"
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            <AnimatePresence mode="wait">
              {filteredImages.map((image) => (
                <motion.div
                  key={`${selectedCategory}-${image.id}`}
                  variants={fadeInUp}
                  initial="initial"
                  animate="animate"
                  exit={{ opacity: 0, scale: 0.9 }}
                  layout
                  className="group cursor-pointer"
                  onClick={() => setSelectedImage(image)}
                >
                  <div className="relative aspect-square rounded-lg overflow-hidden shadow-lg group-hover:shadow-xl transition-all duration-300">
                    <img
                      src={image.src}
                      alt={image.alt}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-moon-navy/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                      <div className="text-center text-moon-white p-4">
                        <h3 className="text-lg font-semibold mb-2">{image.title}</h3>
                        <p className="text-sm opacity-90">Click to view details</p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </motion.div>
        </div>
      </section>

      {/* Lightbox Modal */}
      <AnimatePresence>
        {selectedImage && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedImage(null)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="bg-moon-white rounded-lg max-w-4xl max-h-[90vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="relative">
                <img
                  src={selectedImage.src}
                  alt={selectedImage.alt}
                  className="w-full h-auto max-h-[60vh] object-cover"
                />
                <button
                  onClick={() => setSelectedImage(null)}
                  className="absolute top-4 right-4 w-10 h-10 bg-moon-navy/80 text-moon-white rounded-full flex items-center justify-center hover:bg-moon-navy transition-colors"
                >
                  ✕
                </button>
              </div>
              <div className="p-6">
                <h3 className="text-2xl font-serif font-bold text-moon-navy mb-3">
                  {selectedImage.title}
                </h3>
                <p className="text-moon-navy leading-relaxed">
                  {selectedImage.description}
                </p>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default GalleryPage;
