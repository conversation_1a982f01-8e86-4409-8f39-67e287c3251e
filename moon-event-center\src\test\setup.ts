import '@testing-library/jest-dom'
import { expect, afterEach, beforeAll, afterAll, vi } from 'vitest'
import { cleanup } from '@testing-library/react'
import { server } from './mocks/server'

// Extend Vitest's expect with jest-dom matchers
expect.extend({})

// Setup MSW server
beforeAll(() => {
  server.listen({ onUnhandledRequest: 'error' })
})

afterEach(() => {
  server.resetHandlers()
  cleanup()
})

afterAll(() => {
  server.close()
})

// Mock SEOHead component to avoid react-helmet-async issues
vi.mock('../components/seo/SEOHead', () => ({
  default: () => null,
}))

// Mock framer-motion globally
vi.mock('framer-motion', () => {
  const React = require('react')
  return {
    motion: {
      div: ({ children, ...props }: any) => React.createElement('div', props, children),
      section: ({ children, ...props }: any) => React.createElement('section', props, children),
      h1: ({ children, ...props }: any) => React.createElement('h1', props, children),
      h2: ({ children, ...props }: any) => React.createElement('h2', props, children),
      h3: ({ children, ...props }: any) => React.createElement('h3', props, children),
      p: ({ children, ...props }: any) => React.createElement('p', props, children),
      button: ({ children, ...props }: any) => React.createElement('button', props, children),
      img: ({ children, ...props }: any) => React.createElement('img', props, children),
      a: ({ children, ...props }: any) => React.createElement('a', props, children),
    },
    AnimatePresence: ({ children }: any) => children,
  }
})

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
}

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
}

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: (query: string) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: () => {},
    removeListener: () => {},
    addEventListener: () => {},
    removeEventListener: () => {},
    dispatchEvent: () => {},
  }),
})

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: () => {},
})

// Mock HTMLMediaElement methods
Object.defineProperty(HTMLMediaElement.prototype, 'play', {
  writable: true,
  value: async () => {},
})

Object.defineProperty(HTMLMediaElement.prototype, 'pause', {
  writable: true,
  value: () => {},
})

Object.defineProperty(HTMLMediaElement.prototype, 'load', {
  writable: true,
  value: () => {},
})
