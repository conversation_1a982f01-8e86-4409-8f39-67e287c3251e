import React, { useEffect, useState } from 'react';

interface AccessibilityIssue {
  type: 'error' | 'warning' | 'info';
  element: string;
  issue: string;
  suggestion: string;
  wcagLevel: 'A' | 'AA' | 'AAA';
}

const AccessibilityAudit: React.FC = () => {
  const [issues, setIssues] = useState<AccessibilityIssue[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Only run in development mode
    if (process.env.NODE_ENV !== 'development') return;

    const auditAccessibility = () => {
      const foundIssues: AccessibilityIssue[] = [];

      // Check for missing alt text on images (skip decorative images)
      const images = document.querySelectorAll('img');
      images.forEach((img, index) => {
        // Skip images marked as decorative with aria-hidden="true"
        if (img.getAttribute('aria-hidden') === 'true') {
          return;
        }

        if (!img.alt && !img.getAttribute('aria-label')) {
          foundIssues.push({
            type: 'error',
            element: `Image ${index + 1}`,
            issue: 'Missing alt text',
            suggestion: 'Add descriptive alt text or aria-label for screen readers',
            wcagLevel: 'A'
          });
        }
      });

      // Check for missing form labels
      const inputs = document.querySelectorAll('input, textarea, select');
      inputs.forEach((input, index) => {
        const hasLabel = input.getAttribute('aria-label') || 
                        input.getAttribute('aria-labelledby') ||
                        document.querySelector(`label[for="${input.id}"]`);
        
        if (!hasLabel) {
          foundIssues.push({
            type: 'error',
            element: `Form input ${index + 1}`,
            issue: 'Missing label',
            suggestion: 'Add a label element or aria-label attribute',
            wcagLevel: 'A'
          });
        }
      });

      // Check for insufficient color contrast (simplified check)
      const buttons = document.querySelectorAll('button, .btn-primary, .btn-secondary, .btn-outline');
      buttons.forEach((button, index) => {
        const styles = window.getComputedStyle(button);
        const backgroundColor = styles.backgroundColor;
        const color = styles.color;
        
        // This is a simplified check - in production, you'd use a proper contrast ratio calculator
        if (backgroundColor === 'transparent' && color === 'rgb(26, 31, 58)') {
          foundIssues.push({
            type: 'warning',
            element: `Button ${index + 1}`,
            issue: 'Potential contrast issue',
            suggestion: 'Verify color contrast meets WCAG AA standards (4.5:1 ratio)',
            wcagLevel: 'AA'
          });
        }
      });

      // Check for missing heading hierarchy
      const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      let previousLevel = 0;
      headings.forEach((heading, index) => {
        const currentLevel = parseInt(heading.tagName.charAt(1));
        if (currentLevel > previousLevel + 1) {
          foundIssues.push({
            type: 'warning',
            element: `${heading.tagName} ${index + 1}`,
            issue: 'Heading hierarchy skip',
            suggestion: 'Ensure heading levels follow logical order (h1 → h2 → h3, etc.)',
            wcagLevel: 'AA'
          });
        }
        previousLevel = currentLevel;
      });

      // Check for missing focus indicators
      const focusableElements = document.querySelectorAll('a, button, input, textarea, select, [tabindex]');
      focusableElements.forEach((element, index) => {
        const styles = window.getComputedStyle(element, ':focus');
        if (styles.outline === 'none' && !styles.boxShadow.includes('outline')) {
          foundIssues.push({
            type: 'warning',
            element: `Focusable element ${index + 1}`,
            issue: 'Missing focus indicator',
            suggestion: 'Ensure visible focus indicators for keyboard navigation',
            wcagLevel: 'AA'
          });
        }
      });

      // Check for missing ARIA landmarks
      const main = document.querySelector('main');
      const nav = document.querySelector('nav');
      const header = document.querySelector('header');
      const footer = document.querySelector('footer');

      if (!main) {
        foundIssues.push({
          type: 'error',
          element: 'Page structure',
          issue: 'Missing main landmark',
          suggestion: 'Add a <main> element or role="main" to identify main content',
          wcagLevel: 'A'
        });
      }

      if (!nav) {
        foundIssues.push({
          type: 'warning',
          element: 'Page structure',
          issue: 'Missing navigation landmark',
          suggestion: 'Add a <nav> element or role="navigation" for main navigation',
          wcagLevel: 'AA'
        });
      }

      // Check for videos without captions (skip decorative videos)
      const videos = document.querySelectorAll('video');
      videos.forEach((video, index) => {
        // Skip videos marked as decorative with aria-hidden="true"
        if (video.getAttribute('aria-hidden') === 'true') {
          return;
        }

        const hasTrack = video.querySelector('track[kind="captions"], track[kind="subtitles"]');
        if (!hasTrack) {
          foundIssues.push({
            type: 'error',
            element: `Video ${index + 1}`,
            issue: 'Missing captions',
            suggestion: 'Add caption tracks for video content',
            wcagLevel: 'A'
          });
        }
      });

      setIssues(foundIssues);
    };

    // Run audit after a short delay to ensure DOM is ready
    const timer = setTimeout(auditAccessibility, 1000);
    return () => clearTimeout(timer);
  }, []);

  if (process.env.NODE_ENV !== 'development' || issues.length === 0) {
    return null;
  }

  const getIssueColor = (type: AccessibilityIssue['type']) => {
    switch (type) {
      case 'error': return 'text-red-600 bg-red-50 border-red-200';
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'info': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getWcagColor = (level: AccessibilityIssue['wcagLevel']) => {
    switch (level) {
      case 'A': return 'bg-green-100 text-green-800';
      case 'AA': return 'bg-yellow-100 text-yellow-800';
      case 'AAA': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-moon-navy text-moon-white px-4 py-2 rounded-lg shadow-lg hover:bg-moon-navy/90 transition-colors"
        aria-label={`Accessibility audit: ${issues.length} issues found`}
      >
        A11y ({issues.length})
      </button>

      {isVisible && (
        <div className="absolute bottom-12 right-0 w-96 max-h-96 overflow-y-auto bg-white border border-gray-200 rounded-lg shadow-xl">
          <div className="p-4 border-b border-gray-200">
            <h3 className="font-semibold text-gray-900">Accessibility Audit</h3>
            <p className="text-sm text-gray-600">{issues.length} issues found</p>
          </div>
          
          <div className="p-4 space-y-3">
            {issues.map((issue, index) => (
              <div
                key={index}
                className={`p-3 rounded-lg border ${getIssueColor(issue.type)}`}
              >
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-sm">{issue.element}</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${getWcagColor(issue.wcagLevel)}`}>
                    WCAG {issue.wcagLevel}
                  </span>
                </div>
                <p className="text-sm font-medium mb-1">{issue.issue}</p>
                <p className="text-xs opacity-80">{issue.suggestion}</p>
              </div>
            ))}
          </div>
          
          <div className="p-4 border-t border-gray-200 bg-gray-50">
            <p className="text-xs text-gray-600">
              This audit runs only in development mode and provides basic accessibility checks.
              For comprehensive testing, use tools like axe-core or WAVE.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default AccessibilityAudit;
