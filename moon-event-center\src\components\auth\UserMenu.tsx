import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import { 
  UserIcon, 
  Cog6ToothIcon, 
  ArrowRightOnRectangleIcon,
  ChevronDownIcon,
  CalendarIcon,
  HeartIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../../contexts/AuthContext';

const UserMenu: React.FC = () => {
  const { user, logout, isAuthenticated } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleLogout = () => {
    logout();
    setIsOpen(false);
    navigate('/');
  };

  if (!isAuthenticated || !user) {
    return (
      <div className="flex items-center space-x-4">
        <Link
          to="/auth"
          className="text-moon-navy hover:text-moon-gold transition-colors font-medium"
        >
          Sign In
        </Link>
        <Link
          to="/auth"
          className="bg-moon-gold text-white px-4 py-2 rounded-lg hover:bg-moon-gold/90 transition-colors font-medium"
        >
          Get Started
        </Link>
      </div>
    );
  }

  const menuItems = [
    {
      icon: UserIcon,
      label: 'Profile',
      href: '/profile',
      description: 'Manage your account'
    },
    {
      icon: CalendarIcon,
      label: 'My Bookings',
      href: '/bookings',
      description: 'View your events'
    },
    {
      icon: HeartIcon,
      label: 'Favorites',
      href: '/favorites',
      description: 'Saved items'
    },
    {
      icon: Cog6ToothIcon,
      label: 'Settings',
      href: '/settings',
      description: 'Account preferences'
    }
  ];

  // Add staff-specific menu items
  if (user.role === 'staff' || user.role === 'admin') {
    menuItems.unshift({
      icon: Cog6ToothIcon,
      label: 'Staff Dashboard',
      href: '/staff',
      description: 'Manage events & customers'
    });
  }

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 text-moon-navy hover:text-moon-gold transition-colors"
        aria-expanded={isOpen}
        aria-haspopup="true"
        aria-label={`User menu for ${user.firstName} ${user.lastName}`}
      >
        <div className="w-8 h-8 bg-moon-gold rounded-full flex items-center justify-center">
          <span className="text-white text-sm font-medium">
            {user.firstName.charAt(0)}{user.lastName.charAt(0)}
          </span>
        </div>
        <span className="hidden md:block font-medium">
          {user.firstName}
        </span>
        <ChevronDownIcon
          className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          aria-label="Menu toggle"
          role="img"
        />
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 mt-2 w-72 bg-white rounded-lg shadow-xl border border-moon-silver/20 py-2 z-50"
          >
            {/* User Info Header */}
            <div className="px-4 py-3 border-b border-moon-silver/20">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-moon-gold rounded-full flex items-center justify-center">
                  <span className="text-white font-medium">
                    {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                  </span>
                </div>
                <div>
                  <p className="font-medium text-moon-navy">
                    {user.firstName} {user.lastName}
                  </p>
                  <p className="text-sm text-moon-gray">{user.email}</p>
                  {user.role !== 'customer' && (
                    <span className="inline-block px-2 py-1 text-xs bg-moon-gold/10 text-moon-gold rounded-full mt-1">
                      {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Menu Items */}
            <div className="py-2">
              {menuItems.map((item, index) => (
                <Link
                  key={index}
                  to={item.href}
                  onClick={() => setIsOpen(false)}
                  className="flex items-center px-4 py-3 hover:bg-moon-silver/10 transition-colors group"
                >
                  <item.icon className="w-5 h-5 text-moon-gray group-hover:text-moon-gold transition-colors" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-moon-navy group-hover:text-moon-gold transition-colors">
                      {item.label}
                    </p>
                    <p className="text-xs text-moon-gray">
                      {item.description}
                    </p>
                  </div>
                </Link>
              ))}
            </div>

            {/* Logout */}
            <div className="border-t border-moon-silver/20 pt-2">
              <button
                onClick={handleLogout}
                className="flex items-center w-full px-4 py-3 hover:bg-red-50 transition-colors group"
              >
                <ArrowRightOnRectangleIcon className="w-5 h-5 text-moon-gray group-hover:text-red-600 transition-colors" />
                <span className="ml-3 text-sm font-medium text-moon-navy group-hover:text-red-600 transition-colors">
                  Sign Out
                </span>
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default UserMenu;
