import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEOHead from '../components/seo/SEOHead';

const TermsOfServicePage: React.FC = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 30 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  return (
    <>
      <SEOHead
        title="Terms of Service - Moon Event Center"
        description="Terms of Service for Moon Event Center. Please read these terms carefully before using our services."
        keywords={["terms of service", "legal", "Moon Event Center", "event venue", "Richardson TX"]}
      />

      <div className="min-h-screen bg-moon-white">
        {/* Hero Section */}
        <section className="section-padding bg-gradient-to-br from-moon-navy via-moon-navy to-moon-blue text-moon-white">
          <div className="container-max">
            <motion.div
              className="text-center max-w-4xl mx-auto"
              {...fadeInUp}
            >
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                Terms of Service
              </h1>
              <p className="text-xl text-moon-silver mb-8">
                Please read these terms and conditions carefully before using our services.
              </p>
              <div className="bg-red-600/20 border border-red-400 rounded-lg p-4 mb-8">
                <p className="text-red-200 font-semibold">
                  ⚠️ NOTICE: These are generic terms of service for demonstration purposes only. 
                  They are NOT legally binding and should be replaced with actual terms drafted by a qualified attorney.
                </p>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Terms Content */}
        <section className="section-padding">
          <div className="container-max">
            <motion.div
              className="max-w-4xl mx-auto prose prose-lg"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="bg-moon-cream rounded-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-moon-navy mb-4">1. Acceptance of Terms</h2>
                <p className="text-moon-charcoal mb-4">
                  By accessing and using Moon Event Center's services, you accept and agree to be bound by the terms 
                  and provision of this agreement. These terms apply to all visitors, users, and others who access 
                  or use our services.
                </p>
              </div>

              <div className="bg-moon-cream rounded-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-moon-navy mb-4">2. Event Booking and Reservations</h2>
                <p className="text-moon-charcoal mb-4">
                  All event bookings are subject to availability and confirmation. A deposit may be required to 
                  secure your reservation. Booking confirmations will be provided via email within 24-48 hours 
                  of your initial request.
                </p>
                <ul className="list-disc list-inside text-moon-charcoal space-y-2">
                  <li>Deposits are non-refundable unless otherwise specified</li>
                  <li>Final guest counts must be provided 72 hours before the event</li>
                  <li>Setup and breakdown times are included in your rental period</li>
                  <li>Additional fees may apply for extended hours or special requests</li>
                </ul>
              </div>

              <div className="bg-moon-cream rounded-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-moon-navy mb-4">3. Payment Terms</h2>
                <p className="text-moon-charcoal mb-4">
                  Payment schedules and methods will be outlined in your event contract. Generally:
                </p>
                <ul className="list-disc list-inside text-moon-charcoal space-y-2">
                  <li>50% deposit required to secure booking</li>
                  <li>Remaining balance due 30 days before event date</li>
                  <li>Late payment fees may apply</li>
                  <li>We accept major credit cards, checks, and bank transfers</li>
                </ul>
              </div>

              <div className="bg-moon-cream rounded-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-moon-navy mb-4">4. Cancellation Policy</h2>
                <p className="text-moon-charcoal mb-4">
                  Cancellation terms vary based on the timing of your cancellation:
                </p>
                <ul className="list-disc list-inside text-moon-charcoal space-y-2">
                  <li>More than 90 days: 75% refund of payments made</li>
                  <li>60-90 days: 50% refund of payments made</li>
                  <li>30-60 days: 25% refund of payments made</li>
                  <li>Less than 30 days: No refund</li>
                </ul>
              </div>

              <div className="bg-moon-cream rounded-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-moon-navy mb-4">5. Liability and Insurance</h2>
                <p className="text-moon-charcoal mb-4">
                  Moon Event Center maintains general liability insurance. However, clients are responsible for:
                </p>
                <ul className="list-disc list-inside text-moon-charcoal space-y-2">
                  <li>Any damage to the venue caused by guests or vendors</li>
                  <li>Personal property brought to the venue</li>
                  <li>Obtaining additional insurance if required</li>
                  <li>Ensuring all vendors have appropriate insurance coverage</li>
                </ul>
              </div>

              <div className="bg-moon-cream rounded-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-moon-navy mb-4">6. Venue Rules and Regulations</h2>
                <p className="text-moon-charcoal mb-4">
                  All events must comply with local laws and venue policies:
                </p>
                <ul className="list-disc list-inside text-moon-charcoal space-y-2">
                  <li>No smoking inside the venue</li>
                  <li>Alcohol service must comply with local regulations</li>
                  <li>Music and noise levels must respect neighborhood ordinances</li>
                  <li>Decorations must not damage venue property</li>
                  <li>Cleanup requirements as specified in contract</li>
                </ul>
              </div>

              <div className="bg-moon-cream rounded-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-moon-navy mb-4">7. Force Majeure</h2>
                <p className="text-moon-charcoal mb-4">
                  Neither party shall be liable for any failure or delay in performance due to circumstances 
                  beyond their reasonable control, including but not limited to acts of God, natural disasters, 
                  government regulations, or public health emergencies.
                </p>
              </div>

              <div className="bg-moon-cream rounded-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-moon-navy mb-4">8. Contact Information</h2>
                <p className="text-moon-charcoal mb-4">
                  For questions about these terms or our services, please contact us:
                </p>
                <div className="text-moon-charcoal">
                  <p><strong>Moon Event Center</strong></p>
                  <p>1801 N Plano Rd Ste 200</p>
                  <p>Richardson, TX 75081</p>
                  <p>Phone: (*************</p>
                </div>
              </div>

              <div className="text-center mt-12">
                <p className="text-moon-silver mb-6">
                  Last updated: {new Date().toLocaleDateString()}
                </p>
                <Link
                  to="/contact"
                  className="btn-primary inline-block"
                >
                  Contact Us for Questions
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  );
};

export default TermsOfServicePage;
