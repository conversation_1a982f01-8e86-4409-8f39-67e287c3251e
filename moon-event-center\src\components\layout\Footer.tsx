import React from 'react';
import { Link } from 'react-router-dom';
import { 
  MapPinIcon, 
  PhoneIcon, 
  EnvelopeIcon,
  ClockIcon 
} from '@heroicons/react/24/outline';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { name: 'Home', href: '/' },
    { name: 'About Us', href: '/about' },
    { name: 'Gallery', href: '/gallery' },
    { name: 'Services', href: '/services' },
    { name: 'Contact', href: '/contact' },
  ];

  const services = [
    'Wedding Receptions',
    'Corporate Events',
    'Birthday Parties',
    'Quinceañeras',
    'Community Gatherings',
    'Private Parties',
  ];

  return (
    <footer className="bg-moon-navy text-moon-white">
      <div className="container-max section-padding">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-10 h-10 bg-gradient-silver rounded-full flex items-center justify-center">
                <span className="text-moon-navy font-serif font-bold text-lg">M</span>
              </div>
              <div>
                <h3 className="text-xl font-serif font-semibold">Moon Event Center</h3>
                <p className="text-moon-silver text-sm">Richardson, Texas</p>
              </div>
            </div>
            <p className="text-moon-silver mb-4">
              Creating unforgettable moments in an elegant and sophisticated setting. 
              Your special day deserves nothing short of spectacular.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-serif font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    to={link.href}
                    className="text-moon-silver hover:text-moon-white transition-colors duration-300"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-serif font-semibold mb-4">Our Services</h4>
            <ul className="space-y-2">
              {services.map((service) => (
                <li key={service} className="text-moon-silver">
                  {service}
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-serif font-semibold mb-4">Contact Info</h4>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <MapPinIcon className="w-5 h-5 text-moon-gold mt-1 flex-shrink-0" aria-label="Location" role="img" />
                <div className="text-moon-silver">
                  <p>1801 N Plano Rd Ste 200</p>
                  <p>Richardson, TX 75081</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <PhoneIcon className="w-5 h-5 text-moon-gold flex-shrink-0" aria-label="Phone" role="img" />
                <a
                  href="tel:+19725058888"
                  className="text-moon-silver hover:text-moon-white transition-colors"
                >
                  (*************
                </a>
              </div>

              <div className="flex items-center space-x-3">
                <EnvelopeIcon className="w-5 h-5 text-moon-gold flex-shrink-0" aria-label="Email" role="img" />
                <a
                  href="mailto:<EMAIL>"
                  className="text-moon-silver hover:text-moon-white transition-colors"
                >
                  <EMAIL>
                </a>
              </div>

              <div className="flex items-start space-x-3">
                <ClockIcon className="w-5 h-5 text-moon-gold mt-1 flex-shrink-0" aria-label="Hours" role="img" />
                <div className="text-moon-silver">
                  <p>Open ⋅ Closes 2 AM</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-moon-silver/20 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-moon-silver text-sm">
              © {currentYear} Moon Event Center. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link 
                to="/privacy" 
                className="text-moon-silver hover:text-moon-white text-sm transition-colors"
              >
                Privacy Policy
              </Link>
              <Link 
                to="/terms" 
                className="text-moon-silver hover:text-moon-white text-sm transition-colors"
              >
                Terms of Service
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
